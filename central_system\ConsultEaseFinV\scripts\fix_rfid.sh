#!/bin/bash
# fix_rfid.sh - <PERSON><PERSON><PERSON> to fix RFID reader issues
# This script helps diagnose and fix common RFID reader issues on the Raspberry Pi

# Define colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Make sure we're running as root
if [ "$EUID" -ne 0 ]; then
  echo -e "${RED}Please run as root (use sudo).${NC}"
  exit 1
fi

# Function to display section headers
section() {
  echo -e "\n${BLUE}===== $1 =====${NC}\n"
}

# Function to check if a command was successful
check_status() {
  if [ $? -eq 0 ]; then
    echo -e "${GREEN}Success!${NC}"
  else
    echo -e "${RED}Failed!${NC}"
    if [ "$1" = "critical" ]; then
      echo -e "${RED}Critical error. Cannot continue.${NC}"
      exit 1
    fi
  fi
}

section "ConsultEase RFID Reader Fix Utility"
echo "This script will help diagnose and fix RFID reader issues."
echo "Make sure your RFID reader is connected to the Raspberry Pi."

# Change to project root directory
cd "$(dirname "$0")/.." || exit 1
PROJECT_ROOT=$(pwd)
echo -e "${YELLOW}Working from project root:${NC} $PROJECT_ROOT"

# Check for evdev installation
section "Checking for required packages"
echo "Checking if evdev is installed..."
if python3 -c "import evdev" &>/dev/null; then
  echo -e "${GREEN}evdev is installed.${NC}"
else
  echo -e "${YELLOW}evdev is not installed. Installing now...${NC}"
  apt-get update
  apt-get install -y python3-pip python3-evdev
  check_status
fi

# Check for usb-devices tool
echo "Checking for usb-devices tool..."
if command -v usb-devices &>/dev/null; then
  echo -e "${GREEN}usb-devices tool is available.${NC}"
else
  echo -e "${YELLOW}usb-devices tool not found. Installing usbutils...${NC}"
  apt-get update
  apt-get install -y usbutils
  check_status
fi

# List USB devices
section "Checking connected USB devices"
echo "Running lsusb to find all USB devices..."
lsusb
echo

# Look specifically for our target RFID reader (VID:ffff PID:0035)
echo "Looking for RFID reader with VID:ffff PID:0035..."
if lsusb | grep -q "ID ffff:0035"; then
  echo -e "${GREEN}Target RFID reader found!${NC}"
  READER_FOUND=true
else
  echo -e "${YELLOW}Target RFID reader not found. Check your USB connections.${NC}"
  READER_FOUND=false
fi

# Run the debug script to list input devices
section "Checking input devices"
echo "Running debug_rfid.py to list all input devices..."
python3 scripts/debug_rfid.py list

# Check and fix permissions for input devices
section "Checking input device permissions"
echo "Making sure input devices have correct permissions..."
chmod -R a+r /dev/input/
check_status

# Find the most likely RFID reader device
echo -e "\n${YELLOW}Based on the list above, identify your RFID reader device number.${NC}"
echo -e "Look for devices with high 'RFID reader' likelihood scores."
echo -e "If your device has VID:ffff PID:0035, it should have been highlighted."

# Ask user to choose a device
read -p "Enter the device number or path to test (e.g., '3' or '/dev/input/event3'): " DEVICE_ID

if [ -z "$DEVICE_ID" ]; then
  echo -e "${RED}No device selected. Exiting.${NC}"
  exit 1
fi

# Test with the selected device
section "Testing RFID reader"
echo "Testing RFID reading with device: $DEVICE_ID"
echo "Please scan an RFID card. Press Ctrl+C when done."
echo -e "${YELLOW}(This test will run for a maximum of 30 seconds)${NC}"

# Run the test with a timeout
timeout 30s python3 scripts/debug_rfid.py test "$DEVICE_ID" || true

# Create a configuration file with the device path
section "Creating configuration"
DEVICE_PATH=$(python3 -c "import sys; sys.path.insert(0, '.'); from scripts.debug_rfid import normalize_device_path; print(normalize_device_path('$DEVICE_ID'))")

echo "Creating .env file with RFID device configuration..."
cat > .env << EOF
# ConsultEase Configuration
# Generated by fix_rfid.sh on $(date)

# RFID Configuration
RFID_DEVICE_PATH=$DEVICE_PATH
RFID_SIMULATION_MODE=false

# Debug mode
DEBUG=true
EOF
check_status

section "Setting up automatic configuration"
echo "Creating systemd service to ensure proper device permissions at boot..."

cat > /etc/udev/rules.d/99-consultease-rfid.rules << EOF
# ConsultEase RFID reader permissions
# Allow read access to input devices for the ConsultEase application

# General input devices
KERNEL=="event*", SUBSYSTEM=="input", MODE="0664", GROUP="input"

# Specific rule for our target RFID reader (VID:ffff PID:0035)
SUBSYSTEM=="input", ATTRS{idVendor}=="ffff", ATTRS{idProduct}=="0035", MODE="0664", GROUP="input", SYMLINK+="consultease-rfid"
EOF
check_status

# Reload udev rules
echo "Reloading udev rules..."
udevadm control --reload-rules
udevadm trigger
check_status

section "Summary and Next Steps"
echo -e "${GREEN}RFID reader configuration complete!${NC}"
echo
echo "Configuration saved:"
echo "- RFID device path: $DEVICE_PATH"
echo "- udev rules: /etc/udev/rules.d/99-consultease-rfid.rules"
echo "- Environment variables: .env file in project root"
echo
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Start the ConsultEase application: python3 central_system/main.py"
echo "2. Test scanning RFID cards"
echo "3. If problems persist, run: python3 scripts/debug_rfid.py list"
echo "   to check if the device is still properly detected"
echo
echo -e "${GREEN}Done!${NC}" 