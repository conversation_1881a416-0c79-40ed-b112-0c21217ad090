# ConsultEase .gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyQt5
*.qrc
*.ui~

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Database
*.db
*.sqlite
*.sqlite3

# Logs
*.log
logs/

# Configuration (keep templates)
config.ini
settings.ini
*.env
.env

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Arduino/ESP32 build files
*.hex
*.bin
*.elf
*.map

# Development artifacts
memory-bank/
.pytest_cache/
.coverage
htmlcov/

# Backup files
*.bak
*.backup
*~

# System files
.fuse_hidden*
.nfs*
